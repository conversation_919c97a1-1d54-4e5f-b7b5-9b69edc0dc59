"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useLiveResults.ts":
/*!*************************************!*\
  !*** ./src/hooks/useLiveResults.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLiveResults: () => (/* binding */ useLiveResults)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useLiveResults() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { maxMatches = 50, enabled = true, pollingInterval = 10000 // 10 segundos como BetExplorer\n     } = options;\n    // Estados\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [totalMatches, setTotalMatches] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('disconnected');\n    // Refs para controle\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const isUnmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const lastModifiedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const retryCountRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    // Constantes\n    const MAX_RETRIES = 3;\n    const RETRY_DELAY = 5000 // 5 segundos\n    ;\n    // Função para fazer requisição HTTP\n    const fetchData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useLiveResults.useCallback[fetchData]\": async ()=>{\n            if (isUnmountedRef.current) return;\n            try {\n                console.log('🔄 Fazendo requisição HTTP para dados ao vivo...');\n                setConnectionStatus('connecting');\n                // Preparar headers com cache HTTP\n                const headers = {\n                    'Content-Type': 'application/json'\n                };\n                // Adicionar If-Modified-Since se temos dados anteriores\n                if (lastModifiedRef.current) {\n                    headers['If-Modified-Since'] = lastModifiedRef.current;\n                    console.log('📅 Enviando If-Modified-Since:', lastModifiedRef.current);\n                }\n                const response = await fetch(\"/api/betexplorer/live-data?maxMatches=\".concat(maxMatches), {\n                    method: 'GET',\n                    headers,\n                    signal: AbortSignal.timeout(15000) // 15 segundos timeout\n                });\n                if (isUnmountedRef.current) return;\n                // Tratar 304 Not Modified\n                if (response.status === 304) {\n                    console.log('✅ Dados não modificados (304) - mantendo dados atuais');\n                    setConnectionStatus('connected');\n                    setError(null);\n                    retryCountRef.current = 0;\n                    return;\n                }\n                if (!response.ok) {\n                    throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                }\n                // Extrair Last-Modified header\n                const lastModified = response.headers.get('Last-Modified');\n                if (lastModified) {\n                    lastModifiedRef.current = lastModified;\n                    console.log('📅 Recebido Last-Modified:', lastModified);\n                }\n                const data = await response.json();\n                if (!data.success) {\n                    throw new Error(data.error || 'Resposta da API indica falha');\n                }\n                console.log('📨 Dados recebidos:', {\n                    matches: data.data.matches.length,\n                    cached: data.cached,\n                    timestamp: data.timestamp\n                });\n                // Atualizar estados\n                console.log('🎯 Atualizando estados do hook:', {\n                    matchesCount: data.data.matches.length,\n                    matches: data.data.matches.map({\n                        \"useLiveResults.useCallback[fetchData]\": (m)=>({\n                                id: m.event_id,\n                                homeTeam: m.homeTeam,\n                                awayTeam: m.awayTeam,\n                                score: m.score,\n                                isLive: m.isLive\n                            })\n                    }[\"useLiveResults.useCallback[fetchData]\"])\n                });\n                setMatches(data.data.matches);\n                setTotalMatches(data.data.totalMatches);\n                setLastUpdated(new Date(data.data.lastUpdated));\n                setLoading(false);\n                setConnectionStatus('connected');\n                setError(null);\n                retryCountRef.current = 0;\n            } catch (err) {\n                if (isUnmountedRef.current) return;\n                const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';\n                console.error('❌ Erro na requisição:', errorMessage);\n                setError(errorMessage);\n                setConnectionStatus('error');\n                // Implementar retry logic\n                retryCountRef.current++;\n                if (retryCountRef.current <= MAX_RETRIES) {\n                    console.log(\"\\uD83D\\uDD04 Tentativa \".concat(retryCountRef.current, \"/\").concat(MAX_RETRIES, \" em \").concat(RETRY_DELAY, \"ms\"));\n                    setTimeout({\n                        \"useLiveResults.useCallback[fetchData]\": ()=>{\n                            if (!isUnmountedRef.current) {\n                                fetchData();\n                            }\n                        }\n                    }[\"useLiveResults.useCallback[fetchData]\"], RETRY_DELAY);\n                } else {\n                    console.log('❌ Máximo de tentativas excedido');\n                    setLoading(false);\n                }\n            }\n        }\n    }[\"useLiveResults.useCallback[fetchData]\"], [\n        maxMatches\n    ]);\n    // Função para iniciar polling\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useLiveResults.useCallback[connect]\": ()=>{\n            if (isUnmountedRef.current || !enabled) return;\n            console.log('🔌 Iniciando polling HTTP...', {\n                interval: pollingInterval,\n                maxMatches\n            });\n            // Limpar intervalo anterior se existir\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n            }\n            // Reset retry counter\n            retryCountRef.current = 0;\n            // Fazer primeira requisição imediatamente\n            fetchData();\n            // Configurar polling\n            intervalRef.current = setInterval({\n                \"useLiveResults.useCallback[connect]\": ()=>{\n                    if (!isUnmountedRef.current && enabled) {\n                        fetchData();\n                    }\n                }\n            }[\"useLiveResults.useCallback[connect]\"], pollingInterval);\n        }\n    }[\"useLiveResults.useCallback[connect]\"], [\n        fetchData,\n        enabled,\n        pollingInterval,\n        maxMatches\n    ]);\n    // Função para parar polling\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useLiveResults.useCallback[disconnect]\": ()=>{\n            console.log('🛑 Parando polling HTTP');\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n                intervalRef.current = null;\n            }\n            setConnectionStatus('disconnected');\n        }\n    }[\"useLiveResults.useCallback[disconnect]\"], []);\n    // Função para refetch manual\n    const refetch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useLiveResults.useCallback[refetch]\": async ()=>{\n            console.log('🔄 Refetch manual solicitado');\n            await fetchData();\n        }\n    }[\"useLiveResults.useCallback[refetch]\"], [\n        fetchData\n    ]);\n    // Effect para iniciar/parar polling baseado em enabled\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useLiveResults.useEffect\": ()=>{\n            if (enabled) {\n                connect();\n            } else {\n                disconnect();\n            }\n            return ({\n                \"useLiveResults.useEffect\": ()=>{\n                    disconnect();\n                }\n            })[\"useLiveResults.useEffect\"];\n        }\n    }[\"useLiveResults.useEffect\"], [\n        enabled,\n        connect,\n        disconnect\n    ]);\n    // Cleanup no unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useLiveResults.useEffect\": ()=>{\n            return ({\n                \"useLiveResults.useEffect\": ()=>{\n                    console.log('🧹 Limpando hook useLiveResults');\n                    isUnmountedRef.current = true;\n                    disconnect();\n                }\n            })[\"useLiveResults.useEffect\"];\n        }\n    }[\"useLiveResults.useEffect\"], [\n        disconnect\n    ]);\n    return {\n        matches,\n        totalMatches,\n        lastUpdated,\n        loading,\n        error,\n        connectionStatus,\n        refetch,\n        disconnect,\n        connect\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useLiveResults.ts\n"));

/***/ })

});