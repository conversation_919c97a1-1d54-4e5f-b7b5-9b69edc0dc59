/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/betexplorer/live-data/route";
exports.ids = ["app/api/betexplorer/live-data/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Flive-data%2Froute&page=%2Fapi%2Fbetexplorer%2Flive-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Flive-data%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Flive-data%2Froute&page=%2Fapi%2Fbetexplorer%2Flive-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Flive-data%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_Administrator_Documents_3_frontend_src_app_api_betexplorer_live_data_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/betexplorer/live-data/route.ts */ \"(rsc)/./src/app/api/betexplorer/live-data/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/betexplorer/live-data/route\",\n        pathname: \"/api/betexplorer/live-data\",\n        filename: \"route\",\n        bundlePath: \"app/api/betexplorer/live-data/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\api\\\\betexplorer\\\\live-data\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Administrator_Documents_3_frontend_src_app_api_betexplorer_live_data_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/betexplorer/live-data/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Flive-data%2Froute&page=%2Fapi%2Fbetexplorer%2Flive-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Flive-data%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/betexplorer/live-data/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/betexplorer/live-data/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/betexplorer-types */ \"(rsc)/./src/lib/betexplorer-types.ts\");\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n\n\n\n// Cache global para detectar mudanças e implementar cache HTTP\nlet cache = null;\nasync function GET(request) {\n    console.log('🔄 Requisição HTTP para dados ao vivo do BetExplorer...');\n    try {\n        // Verificar header If-Modified-Since para cache HTTP\n        const ifModifiedSince = request.headers.get('if-modified-since');\n        const clientLastModified = ifModifiedSince ? new Date(ifModifiedSince) : null;\n        console.log('📅 If-Modified-Since:', ifModifiedSince);\n        // Buscar dados atuais\n        const newData = await fetchLiveData();\n        const newDataHash = JSON.stringify(newData);\n        const now = new Date();\n        // Verificar se temos cache válido\n        if (cache && cache.hash === newDataHash) {\n            console.log('📦 Dados inalterados, usando cache existente');\n            // Se cliente tem dados atualizados, retornar 304 Not Modified\n            if (clientLastModified && clientLastModified >= cache.lastModified) {\n                console.log('✅ Retornando 304 Not Modified - cliente tem dados atuais');\n                return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n                    status: 304,\n                    headers: {\n                        'Last-Modified': cache.lastModified.toUTCString(),\n                        'Cache-Control': 'no-cache, must-revalidate',\n                        'Access-Control-Allow-Origin': '*',\n                        'Access-Control-Expose-Headers': 'Last-Modified'\n                    }\n                });\n            }\n            // Cliente não tem dados atuais, retornar dados do cache\n            console.log('📤 Retornando dados do cache para cliente desatualizado');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: cache.data,\n                timestamp: cache.data.lastUpdated,\n                cached: true\n            }, {\n                headers: {\n                    'Last-Modified': cache.lastModified.toUTCString(),\n                    'Cache-Control': 'no-cache, must-revalidate',\n                    'Access-Control-Allow-Origin': '*',\n                    'Access-Control-Expose-Headers': 'Last-Modified'\n                }\n            });\n        }\n        // Dados mudaram ou não temos cache, atualizar\n        console.log('🔄 Dados mudaram, atualizando cache');\n        cache = {\n            data: newData,\n            lastModified: now,\n            hash: newDataHash\n        };\n        console.log('📤 Retornando dados atualizados:', newData.matches.length, 'jogos');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: newData,\n            timestamp: newData.lastUpdated,\n            cached: false\n        }, {\n            headers: {\n                'Last-Modified': now.toUTCString(),\n                'Cache-Control': 'no-cache, must-revalidate',\n                'Access-Control-Allow-Origin': '*',\n                'Access-Control-Expose-Headers': 'Last-Modified'\n            }\n        });\n    } catch (error) {\n        console.error('❌ Erro ao buscar dados:', error);\n        // Usar dados do cache se disponível em caso de erro\n        if (cache) {\n            console.log('⚠️ Usando dados do cache devido ao erro');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: cache.data,\n                timestamp: cache.data.lastUpdated,\n                cached: true,\n                error: 'Dados do cache devido a erro na API'\n            }, {\n                headers: {\n                    'Last-Modified': cache.lastModified.toUTCString(),\n                    'Cache-Control': 'no-cache, must-revalidate',\n                    'Access-Control-Allow-Origin': '*',\n                    'Access-Control-Expose-Headers': 'Last-Modified'\n                }\n            });\n        }\n        // Sem cache disponível, retornar erro\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Erro ao buscar dados e nenhum cache disponível',\n            data: {\n                matches: [],\n                totalMatches: 0,\n                lastUpdated: new Date().toISOString(),\n                footballOnly: true\n            }\n        }, {\n            status: 500,\n            headers: {\n                'Access-Control-Allow-Origin': '*'\n            }\n        });\n    }\n}\n// Função para buscar dados do BetExplorer (reutilizada do SSE)\nasync function fetchLiveData() {\n    // 1. Buscar live results\n    const liveResultsUrl = `${_lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__.BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/live-results.php`;\n    const liveResponse = await fetch(liveResultsUrl, {\n        method: 'GET',\n        headers: _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__.BETEXPLORER_CONFIG.HEADERS,\n        signal: AbortSignal.timeout(10000)\n    });\n    if (!liveResponse.ok) {\n        throw new Error(`Live Results API retornou status ${liveResponse.status}`);\n    }\n    const liveData = await liveResponse.json();\n    if (!liveData || !liveData.events) {\n        throw new Error('Dados de live results inválidos');\n    }\n    console.log(`📊 Eventos recebidos: ${Object.keys(liveData.events).length}`);\n    // 2. Buscar detalhes dos jogos (opcional - pode falhar)\n    let matchDetailsData = {\n        events: {}\n    };\n    try {\n        const matchDetailsUrl = `${_lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__.BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/match-content.php`;\n        const matchDetailsResponse = await fetch(matchDetailsUrl, {\n            method: 'GET',\n            headers: _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__.BETEXPLORER_CONFIG.HEADERS,\n            signal: AbortSignal.timeout(10000)\n        });\n        if (matchDetailsResponse.ok) {\n            const responseText = await matchDetailsResponse.text();\n            try {\n                matchDetailsData = JSON.parse(responseText);\n                console.log(`📋 Detalhes de jogos recebidos: ${Object.keys(matchDetailsData.events || {}).length}`);\n            } catch (parseError) {\n                console.log('⚠️ Resposta de match-content não é JSON válido, usando dados básicos');\n                matchDetailsData = {\n                    events: {}\n                };\n            }\n        } else {\n            console.log('⚠️ Match Content API falhou, usando dados básicos');\n        }\n    } catch (error) {\n        console.log('⚠️ Erro ao buscar detalhes dos jogos, usando dados básicos:', error);\n    }\n    // 3. Processar e combinar dados\n    const matches = [];\n    Object.entries(liveData.events).forEach(([eventId, event])=>{\n        // Filtrar apenas futebol\n        if (event.sport_id !== _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_1__.SPORT_IDS.FOOTBALL) {\n            return;\n        }\n        // Debug: log da estrutura do evento (apenas primeiro evento)\n        if (matches.length === 0) {\n            console.log(`🔍 Evento ${eventId}:`, {\n                keys: Object.keys(event),\n                name: event.name,\n                home_name: event.home_name,\n                away_name: event.away_name,\n                score: event.score,\n                event_stage_id: event.event_stage_id\n            });\n        }\n        // Buscar detalhes do jogo\n        const matchDetails = matchDetailsData.events?.[eventId];\n        const stageName = liveData.stages?.[event.event_stage_id]?.name || 'Competição';\n        // Extrair nomes dos times - usar dados do live results se disponível\n        let homeTeam = 'Time Casa';\n        let awayTeam = 'Time Visitante';\n        let homeTeamLogo;\n        let awayTeamLogo;\n        // Tentar extrair nomes dos times do próprio evento live results\n        if (event.home_name && event.away_name) {\n            homeTeam = event.home_name;\n            awayTeam = event.away_name;\n        } else if (event.name) {\n            // Se tiver nome do evento, tentar extrair os times\n            const eventName = event.name.toString();\n            if (eventName.includes(' - ')) {\n                const teams = eventName.split(' - ');\n                if (teams.length === 2) {\n                    homeTeam = teams[0].trim();\n                    awayTeam = teams[1].trim();\n                }\n            } else if (eventName.includes(' vs ')) {\n                const teams = eventName.split(' vs ');\n                if (teams.length === 2) {\n                    homeTeam = teams[0].trim();\n                    awayTeam = teams[1].trim();\n                }\n            }\n        }\n        // Fallback: tentar extrair do match details se disponível\n        if ((homeTeam === 'Time Casa' || awayTeam === 'Time Visitante') && matchDetails?.content) {\n            const $ = cheerio__WEBPACK_IMPORTED_MODULE_2__.load(matchDetails.content);\n            // Extrair nomes dos times\n            const teamElements = $('.list-breadcrumb-first a');\n            if (teamElements.length >= 2) {\n                homeTeam = $(teamElements[0]).text().trim();\n                awayTeam = $(teamElements[1]).text().trim();\n            }\n            // Extrair logos dos times\n            const logoElements = $('.list-breadcrumb-first img');\n            if (logoElements.length >= 2) {\n                homeTeamLogo = $(logoElements[0]).attr('src');\n                awayTeamLogo = $(logoElements[1]).attr('src');\n            }\n        }\n        console.log(`🏆 Processando jogo: ${homeTeam} vs ${awayTeam} (${event.score})`);\n        const match = {\n            event_id: eventId,\n            homeTeam,\n            awayTeam,\n            homeTeamLogo,\n            awayTeamLogo,\n            score: event.score || '0:0',\n            minute: parseInt(event.minute) || 0,\n            finished: event.finished === 1,\n            competition: stageName,\n            country: '',\n            isLive: event.finished === 0 && parseInt(event.minute) > 0,\n            odds: {\n                live_odds_1x2: event.live_odds_1x2,\n                live_odds_ou: event.live_odds_ou,\n                live_odds_dc: event.live_odds_dc,\n                live_odds_dnb: event.live_odds_dnb,\n                live_odds_btts: event.live_odds_btts,\n                live_odds_ah: event.live_odds_ah\n            }\n        };\n        matches.push(match);\n    });\n    console.log(`✅ Processados ${matches.length} jogos de futebol`);\n    return {\n        matches,\n        totalMatches: matches.length,\n        lastUpdated: new Date().toISOString(),\n        footballOnly: true\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/betexplorer/live-data/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/betexplorer-types.ts":
/*!**************************************!*\
  !*** ./src/lib/betexplorer-types.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BETEXPLORER_CONFIG: () => (/* binding */ BETEXPLORER_CONFIG),\n/* harmony export */   MARKET_TYPES: () => (/* binding */ MARKET_TYPES),\n/* harmony export */   SPORT_IDS: () => (/* binding */ SPORT_IDS)\n/* harmony export */ });\n/**\n * Tipos TypeScript para as APIs do BetExplorer\n * Baseado na documentação completa das APIs descobertas\n */ // ============================================================================\n// TIPOS BASE\n// ============================================================================\n// ============================================================================\n// CONFIGURAÇÕES E CONSTANTES\n// ============================================================================\nconst BETEXPLORER_CONFIG = {\n    BASE_URL: 'https://www.betexplorer.com',\n    RATE_LIMIT_MS: 2000,\n    CACHE_TTL_MS: 30000,\n    HEADERS: {\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n        'Accept': 'application/json, text/html, */*',\n        'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',\n        'X-Requested-With': 'XMLHttpRequest',\n        'Referer': 'https://www.betexplorer.com'\n    }\n};\nconst SPORT_IDS = {\n    FOOTBALL: 1,\n    TENNIS: 2,\n    BASKETBALL: 3,\n    HOCKEY: 4\n};\nconst MARKET_TYPES = {\n    ODDS_1X2: '1x2',\n    OVER_UNDER: 'ou',\n    ASIAN_HANDICAP: 'ah',\n    DRAW_NO_BET: 'dnb',\n    DOUBLE_CHANCE: 'dc',\n    BOTH_TEAMS_TO_SCORE: 'btts'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/betexplorer-types.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:sqlite");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/undici","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/cheerio","vendor-chunks/css-select","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/htmlparser2","vendor-chunks/whatwg-mimetype","vendor-chunks/nth-check","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/safer-buffer","vendor-chunks/boolbase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Flive-data%2Froute&page=%2Fapi%2Fbetexplorer%2Flive-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Flive-data%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();