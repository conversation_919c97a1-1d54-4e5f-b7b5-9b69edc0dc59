"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx":
/*!************************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_positions_table__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/positions-table */ \"(app-pages-browser)/./src/components/positions-table.tsx\");\n/* harmony import */ var _components_calculator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/calculator */ \"(app-pages-browser)/./src/components/calculator.tsx\");\n/* harmony import */ var _components_positions_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/positions-header */ \"(app-pages-browser)/./src/components/positions-header.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,RefreshCw,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    console.log('🎯 Dashboard iniciando...');\n    // Teste simples com useState e useEffect\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('connecting');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            let retryCount = 0;\n            const maxRetries = 3;\n            const retryDelay = 5000 // 5 segundos\n            ;\n            const connectSSE = {\n                \"Dashboard.useEffect.connectSSE\": ()=>{\n                    console.log('🔌 Conectando ao SSE diretamente...', retryCount > 0 ? \"(tentativa \".concat(retryCount + 1, \")\") : '');\n                    const eventSource = new EventSource('/api/betexplorer/live-stream');\n                    eventSource.onopen = ({\n                        \"Dashboard.useEffect.connectSSE\": ()=>{\n                            console.log('✅ Conexão SSE estabelecida');\n                            setConnectionStatus('connected');\n                            retryCount = 0; // Reset retry count on successful connection\n                        }\n                    })[\"Dashboard.useEffect.connectSSE\"];\n                    eventSource.onmessage = ({\n                        \"Dashboard.useEffect.connectSSE\": (event)=>{\n                            console.log('📨 Mensagem recebida:', event.data);\n                            try {\n                                const data = JSON.parse(event.data);\n                                console.log('📊 Dados processados:', data);\n                                if (data.matches) {\n                                    console.log('🏆 Jogos recebidos:', data.matches.map({\n                                        \"Dashboard.useEffect.connectSSE\": (m)=>({\n                                                id: m.event_id,\n                                                homeTeam: m.homeTeam,\n                                                awayTeam: m.awayTeam,\n                                                hasHomeLogo: !!m.homeTeamLogo,\n                                                hasAwayLogo: !!m.awayTeamLogo,\n                                                homeLogoUrl: m.homeTeamLogo,\n                                                awayLogoUrl: m.awayTeamLogo\n                                            })\n                                    }[\"Dashboard.useEffect.connectSSE\"]));\n                                    setMatches(data.matches);\n                                    setLoading(false);\n                                    setError(null);\n                                }\n                            } catch (err) {\n                                console.error('❌ Erro ao processar:', err);\n                            }\n                        }\n                    })[\"Dashboard.useEffect.connectSSE\"];\n                    eventSource.onerror = ({\n                        \"Dashboard.useEffect.connectSSE\": (event)=>{\n                            console.error('❌ Erro SSE:', event);\n                            console.log('📊 Estado da conexão SSE:', eventSource.readyState);\n                            setConnectionStatus('error');\n                            eventSource.close();\n                            // Retry logic\n                            if (retryCount < maxRetries) {\n                                retryCount++;\n                                console.log(\"\\uD83D\\uDD04 Tentando reconectar em \".concat(retryDelay / 1000, \"s... (\").concat(retryCount, \"/\").concat(maxRetries, \")\"));\n                                setTimeout(connectSSE, retryDelay);\n                            } else {\n                                console.log('❌ Máximo de tentativas atingido');\n                                setError('Falha na conexão após múltiplas tentativas');\n                            }\n                        }\n                    })[\"Dashboard.useEffect.connectSSE\"];\n                    return eventSource;\n                }\n            }[\"Dashboard.useEffect.connectSSE\"];\n            const eventSource = connectSSE();\n            return ({\n                \"Dashboard.useEffect\": ()=>{\n                    console.log('🛑 Fechando SSE');\n                    eventSource.close();\n                }\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const reconnect = ()=>{\n        console.log('🔄 Reconectando manualmente...');\n        setConnectionStatus('connecting');\n        setError(null);\n        window.location.reload();\n    };\n    console.log('🎯 Dashboard renderizado:', {\n        matchesCount: matches.length,\n        loading,\n        error,\n        connectionStatus\n    });\n    // Usar todos os jogos recebidos (sem limite)\n    const dashboardMatches = matches.length > 0 ? matches : [];\n    console.log('📊 Total de jogos para exibir:', dashboardMatches.length);\n    // Ícone de status da conexão\n    const getConnectionIcon = ()=>{\n        switch(connectionStatus){\n            case 'connected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-3 w-3 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 16\n                }, this);\n            case 'connecting':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-3 w-3 text-yellow-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 16\n                }, this);\n            case 'disconnected':\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-3 w-3 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_RefreshCw_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-3 w-3 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full grid grid-cols-[2fr_1fr] gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col h-full gap-1 overflow-y-auto custom-scrollbar\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_positions_header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        getConnectionIcon(),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-muted-foreground capitalize\",\n                                            children: connectionStatus\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    dashboardMatches.length > 0 ? dashboardMatches.map((match, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_positions_table__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                matchData: match,\n                                loading: loading,\n                                error: error,\n                                onRefresh: reconnect\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, this)\n                        }, (match === null || match === void 0 ? void 0 : match.event_id) || \"fallback-\".concat(index), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this)) : // Fallback quando não há dados\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_positions_table__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    loading: loading,\n                                    error: error,\n                                    onRefresh: reconnect,\n                                    gameTitle: \"Real Madrid x Inter de Mil\\xe3o\",\n                                    league: \"Champions League\",\n                                    time: \"Hoje, 20:00\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_positions_table__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    loading: loading,\n                                    error: error,\n                                    onRefresh: reconnect,\n                                    gameTitle: \"Barcelona x PSG\",\n                                    league: \"Champions League\",\n                                    time: \"Amanh\\xe3, 16:45\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_positions_table__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    loading: loading,\n                                    error: error,\n                                    onRefresh: reconnect,\n                                    gameTitle: \"Manchester City x Arsenal\",\n                                    league: \"Premier League\",\n                                    time: \"S\\xe1bado, 14:30\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_positions_table__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    loading: loading,\n                                    error: error,\n                                    onRefresh: reconnect,\n                                    gameTitle: \"Bayern Munich x Borussia Dortmund\",\n                                    league: \"Bundesliga\",\n                                    time: \"Domingo, 17:00\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_calculator__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"CMpwdfSVy0Gd7dj32nL9y5oN6b0=\");\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/dashboard/page.tsx\n"));

/***/ })

});