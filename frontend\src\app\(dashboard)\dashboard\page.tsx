'use client'

import PositionsTable from "@/components/positions-table"
import Calculator from "@/components/calculator"
import PositionsHeader from "@/components/positions-header"
import { useLiveResults } from "@/hooks/useLiveResults"

import { RefreshCw, Wifi, WifiOff, AlertCircle } from "lucide-react"

export default function Dashboard() {
  console.log('🎯 Dashboard iniciando com polling HTTP...')

  // Usar novo hook de polling HTTP
  const {
    matches,
    totalMatches,
    lastUpdated,
    loading,
    error,
    connectionStatus,
    refetch
  } = useLiveResults({
    maxMatches: 50,
    enabled: true,
    pollingInterval: 10000 // 10 segundos como BetExplorer
  })

  console.log('🎯 Dashboard renderizado:', {
    matchesCount: matches.length,
    totalMatches,
    loading,
    error,
    connectionStatus,
    lastUpdated,
    matchesPreview: matches.slice(0, 3).map(m => ({
      id: m.event_id,
      homeTeam: m.homeTeam,
      awayTeam: m.awayTeam,
      score: m.score
    }))
  })

  // Usar todos os jogos recebidos
  const dashboardMatches = matches.length > 0 ? matches : []

  console.log('📊 Total de jogos para exibir:', dashboardMatches.length)
  console.log('📋 Primeiros 3 jogos:', dashboardMatches.slice(0, 3).map(m => ({
    id: m.event_id,
    homeTeam: m.homeTeam,
    awayTeam: m.awayTeam,
    score: m.score,
    competition: m.competition
  })))

  // Ícone de status da conexão
  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Wifi className="h-3 w-3 text-green-500" />
      case 'connecting':
        return <RefreshCw className="h-3 w-3 text-yellow-500 animate-spin" />
      case 'disconnected':
      case 'error':
        return <WifiOff className="h-3 w-3 text-red-500" />
      default:
        return <AlertCircle className="h-3 w-3 text-gray-500" />
    }
  }
  return (
    <div className="h-full grid grid-cols-[2fr_1fr] gap-4">
      {/* Coluna esquerda - Múltiplos Jogos */}
      <div className="flex flex-col h-full gap-1 overflow-y-auto custom-scrollbar">
        {/* Header único para todas as posições */}
        <div className="flex items-center justify-between mb-2">
          <PositionsHeader />
          <div className="flex items-center gap-2">
            {/* Status da conexão */}
            <div className="flex items-center gap-1">
              {getConnectionIcon()}
              <span className="text-xs text-muted-foreground capitalize">
                {connectionStatus}
              </span>
            </div>

            
           
          </div>
        </div>

        {/* Renderizar todos os jogos dinamicamente */}
        {dashboardMatches.length > 0 ? (
          dashboardMatches.map((match, index) => (
            <div key={match?.event_id || `fallback-${index}`} className="bg-card">
              <PositionsTable
                matchData={match}
                loading={loading}
                error={error}
                onRefresh={refetch}
              />
            </div>
          ))
        ) : (
          // Fallback quando não há dados
          <>
            <div className="bg-card">
              <PositionsTable
                loading={loading}
                error={error}
                onRefresh={refetch}
                gameTitle="Real Madrid x Inter de Milão"
                league="Champions League"
                time="Hoje, 20:00"
              />
            </div>
            <div className="bg-card">
              <PositionsTable
                loading={loading}
                error={error}
                onRefresh={refetch}
                gameTitle="Barcelona x PSG"
                league="Champions League"
                time="Amanhã, 16:45"
              />
            </div>
            <div className="bg-card">
              <PositionsTable
                loading={loading}
                error={error}
                onRefresh={refetch}
                gameTitle="Manchester City x Arsenal"
                league="Premier League"
                time="Sábado, 14:30"
              />
            </div>
            <div className="bg-card">
              <PositionsTable
                loading={loading}
                error={error}
                onRefresh={refetch}
                gameTitle="Bayern Munich x Borussia Dortmund"
                league="Bundesliga"
                time="Domingo, 17:00"
              />
            </div>
          </>
        )}
      </div>

      {/* Coluna direita - Calculadora */}
      <div>
        <Calculator />
      </div>
    </div>
  );
}
