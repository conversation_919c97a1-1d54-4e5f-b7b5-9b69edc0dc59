import { NextRequest, NextResponse } from 'next/server'
import { 
  DashboardMatch,
  BETEXPLORER_CONFIG,
  SPORT_IDS 
} from '@/lib/betexplorer-types'
import * as cheerio from 'cheerio'

/**
 * API Route HTTP para dados em tempo real do BetExplorer
 * Substitui SSE por polling HTTP simples com cache inteligente
 * Segue padrão do BetExplorer: polling a cada 10 segundos
 */

interface LiveStreamData {
  matches: DashboardMatch[]
  totalMatches: number
  lastUpdated: string
  footballOnly: boolean
}

interface CacheEntry {
  data: LiveStreamData
  lastModified: Date
  hash: string
}

// Cache global para detectar mudanças e implementar cache HTTP
let cache: CacheEntry | null = null

export async function GET(request: NextRequest) {
  console.log('🔄 Requisição HTTP para dados ao vivo do BetExplorer...')

  try {
    // Verificar header If-Modified-Since para cache HTTP
    const ifModifiedSince = request.headers.get('if-modified-since')
    const clientLastModified = ifModifiedSince ? new Date(ifModifiedSince) : null

    console.log('📅 If-Modified-Since:', ifModifiedSince)

    // Buscar dados atuais
    const newData = await fetchLiveData()
    const newDataHash = JSON.stringify(newData)
    const now = new Date()

    // Verificar se temos cache válido
    if (cache && cache.hash === newDataHash) {
      console.log('📦 Dados inalterados, usando cache existente')
      
      // Se cliente tem dados atualizados, retornar 304 Not Modified
      if (clientLastModified && clientLastModified >= cache.lastModified) {
        console.log('✅ Retornando 304 Not Modified - cliente tem dados atuais')
        return new NextResponse(null, { 
          status: 304,
          headers: {
            'Last-Modified': cache.lastModified.toUTCString(),
            'Cache-Control': 'no-cache, must-revalidate',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Expose-Headers': 'Last-Modified'
          }
        })
      }

      // Cliente não tem dados atuais, retornar dados do cache
      console.log('📤 Retornando dados do cache para cliente desatualizado')
      return NextResponse.json({
        success: true,
        data: cache.data,
        timestamp: cache.data.lastUpdated,
        cached: true
      }, {
        headers: {
          'Last-Modified': cache.lastModified.toUTCString(),
          'Cache-Control': 'no-cache, must-revalidate',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Expose-Headers': 'Last-Modified'
        }
      })
    }

    // Dados mudaram ou não temos cache, atualizar
    console.log('🔄 Dados mudaram, atualizando cache')
    cache = {
      data: newData,
      lastModified: now,
      hash: newDataHash
    }

    console.log('📤 Retornando dados atualizados:', newData.matches.length, 'jogos')
    
    return NextResponse.json({
      success: true,
      data: newData,
      timestamp: newData.lastUpdated,
      cached: false
    }, {
      headers: {
        'Last-Modified': now.toUTCString(),
        'Cache-Control': 'no-cache, must-revalidate',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Expose-Headers': 'Last-Modified'
      }
    })

  } catch (error) {
    console.error('❌ Erro ao buscar dados:', error)

    // Usar dados do cache se disponível em caso de erro
    if (cache) {
      console.log('⚠️ Usando dados do cache devido ao erro')
      return NextResponse.json({
        success: true,
        data: cache.data,
        timestamp: cache.data.lastUpdated,
        cached: true,
        error: 'Dados do cache devido a erro na API'
      }, {
        headers: {
          'Last-Modified': cache.lastModified.toUTCString(),
          'Cache-Control': 'no-cache, must-revalidate',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Expose-Headers': 'Last-Modified'
        }
      })
    }

    // Sem cache disponível, retornar erro
    return NextResponse.json({
      success: false,
      error: 'Erro ao buscar dados e nenhum cache disponível',
      data: {
        matches: [],
        totalMatches: 0,
        lastUpdated: new Date().toISOString(),
        footballOnly: true
      }
    }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*'
      }
    })
  }
}

// Função para buscar dados do BetExplorer (reutilizada do SSE)
async function fetchLiveData(): Promise<LiveStreamData> {
  // 1. Buscar live results
  const liveResultsUrl = `${BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/live-results.php`
  
  const liveResponse = await fetch(liveResultsUrl, {
    method: 'GET',
    headers: BETEXPLORER_CONFIG.HEADERS,
    signal: AbortSignal.timeout(10000)
  })

  if (!liveResponse.ok) {
    throw new Error(`Live Results API retornou status ${liveResponse.status}`)
  }

  const liveData = await liveResponse.json()
  
  if (!liveData || !liveData.events) {
    throw new Error('Dados de live results inválidos')
  }

  console.log(`📊 Eventos recebidos: ${Object.keys(liveData.events).length}`)

  // 2. Buscar detalhes dos jogos (opcional - pode falhar)
  let matchDetailsData: any = { events: {} }

  try {
    const matchDetailsUrl = `${BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/match-content.php`

    const matchDetailsResponse = await fetch(matchDetailsUrl, {
      method: 'GET',
      headers: BETEXPLORER_CONFIG.HEADERS,
      signal: AbortSignal.timeout(10000)
    })

    if (matchDetailsResponse.ok) {
      const responseText = await matchDetailsResponse.text()
      try {
        matchDetailsData = JSON.parse(responseText)
        console.log(`📋 Detalhes de jogos recebidos: ${Object.keys(matchDetailsData.events || {}).length}`)
      } catch (parseError) {
        console.log('⚠️ Resposta de match-content não é JSON válido, usando dados básicos')
        matchDetailsData = { events: {} }
      }
    } else {
      console.log('⚠️ Match Content API falhou, usando dados básicos')
    }
  } catch (error) {
    console.log('⚠️ Erro ao buscar detalhes dos jogos, usando dados básicos:', error)
  }

  // 3. Processar e combinar dados
  const matches: DashboardMatch[] = []

  Object.entries(liveData.events).forEach(([eventId, event]: [string, any]) => {
    // Filtrar apenas futebol
    if (event.sport_id !== SPORT_IDS.FOOTBALL) {
      return
    }

    // Buscar detalhes do jogo
    const matchDetails = matchDetailsData.events?.[eventId]
    const stageName = liveData.stages?.[event.event_stage_id]?.name || 'Competição'

    // Extrair nomes dos times
    let homeTeam = 'Time Casa'
    let awayTeam = 'Time Visitante'
    let homeTeamLogo: string | undefined
    let awayTeamLogo: string | undefined

    if (matchDetails?.content) {
      const $ = cheerio.load(matchDetails.content)
      
      // Extrair nomes dos times
      const teamElements = $('.list-breadcrumb-first a')
      if (teamElements.length >= 2) {
        homeTeam = $(teamElements[0]).text().trim()
        awayTeam = $(teamElements[1]).text().trim()
      }

      // Extrair logos dos times
      const logoElements = $('.list-breadcrumb-first img')
      if (logoElements.length >= 2) {
        homeTeamLogo = $(logoElements[0]).attr('src')
        awayTeamLogo = $(logoElements[1]).attr('src')
      }
    }

    const match: DashboardMatch = {
      event_id: eventId,
      homeTeam,
      awayTeam,
      homeTeamLogo,
      awayTeamLogo,
      score: event.score || '0:0',
      minute: parseInt(event.minute) || 0,
      finished: event.finished === 1,
      competition: stageName,
      country: '',
      isLive: event.finished === 0 && parseInt(event.minute) > 0,
      odds: {
        live_odds_1x2: event.live_odds_1x2 as Record<string, number[]> | undefined,
        live_odds_ou: event.live_odds_ou as Record<string, number[]> | undefined,
        live_odds_dc: event.live_odds_dc as Record<string, number[]> | undefined,
        live_odds_dnb: event.live_odds_dnb as Record<string, number[]> | undefined,
        live_odds_btts: event.live_odds_btts as Record<string, number[]> | undefined,
        live_odds_ah: event.live_odds_ah as Record<string, number[]> | undefined
      }
    }

    matches.push(match)
  })

  console.log(`✅ Processados ${matches.length} jogos de futebol`)

  return {
    matches,
    totalMatches: matches.length,
    lastUpdated: new Date().toISOString(),
    footballOnly: true
  }
}
