import { useState, useEffect, useCallback, useRef } from 'react'
import { DashboardMatch } from '@/lib/betexplorer-types'

/**
 * Hook para consumir dados ao vivo via polling HTTP
 * Substitui o SSE por requisições HTTP simples seguindo padrão do BetExplorer
 * Polling a cada 10 segundos com cache HTTP inteligente
 */

interface LiveStreamData {
  matches: DashboardMatch[]
  totalMatches: number
  lastUpdated: string
  footballOnly: boolean
}

interface ApiResponse {
  success: boolean
  data: LiveStreamData
  timestamp: string
  cached: boolean
  error?: string
}

type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error'

interface UseLiveResultsOptions {
  maxMatches?: number
  enabled?: boolean
  pollingInterval?: number // em milissegundos, padrão 10000 (10s)
}

interface UseLiveResultsReturn {
  matches: DashboardMatch[]
  totalMatches: number
  lastUpdated: Date | null
  loading: boolean
  error: string | null
  connectionStatus: ConnectionStatus
  refetch: () => Promise<void>
  disconnect: () => void
  connect: () => void
}

export function useLiveResults(options: UseLiveResultsOptions = {}): UseLiveResultsReturn {
  const {
    maxMatches = 50,
    enabled = true,
    pollingInterval = 10000 // 10 segundos como BetExplorer
  } = options

  // Estados
  const [matches, setMatches] = useState<DashboardMatch[]>([])
  const [totalMatches, setTotalMatches] = useState(0)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected')

  // Refs para controle
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const isUnmountedRef = useRef(false)
  const lastModifiedRef = useRef<string | null>(null)
  const retryCountRef = useRef(0)

  // Constantes
  const MAX_RETRIES = 3
  const RETRY_DELAY = 5000 // 5 segundos

  // Função para fazer requisição HTTP
  const fetchData = useCallback(async (): Promise<void> => {
    if (isUnmountedRef.current) return

    try {
      console.log('🔄 Fazendo requisição HTTP para dados ao vivo...')
      setConnectionStatus('connecting')

      // Preparar headers com cache HTTP
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      }

      // Adicionar If-Modified-Since se temos dados anteriores
      if (lastModifiedRef.current) {
        headers['If-Modified-Since'] = lastModifiedRef.current
        console.log('📅 Enviando If-Modified-Since:', lastModifiedRef.current)
      }

      const response = await fetch(`/api/betexplorer/live-data?maxMatches=${maxMatches}`, {
        method: 'GET',
        headers,
        signal: AbortSignal.timeout(15000) // 15 segundos timeout
      })

      if (isUnmountedRef.current) return

      // Tratar 304 Not Modified
      if (response.status === 304) {
        console.log('✅ Dados não modificados (304) - mantendo dados atuais')
        setConnectionStatus('connected')
        setError(null)
        retryCountRef.current = 0
        return
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      // Extrair Last-Modified header
      const lastModified = response.headers.get('Last-Modified')
      if (lastModified) {
        lastModifiedRef.current = lastModified
        console.log('📅 Recebido Last-Modified:', lastModified)
      }

      const data: ApiResponse = await response.json()

      if (!data.success) {
        throw new Error(data.error || 'Resposta da API indica falha')
      }

      console.log('📨 Dados recebidos:', {
        matches: data.data.matches.length,
        cached: data.cached,
        timestamp: data.timestamp
      })

      // Atualizar estados
      setMatches(data.data.matches)
      setTotalMatches(data.data.totalMatches)
      setLastUpdated(new Date(data.data.lastUpdated))
      setLoading(false)
      setConnectionStatus('connected')
      setError(null)
      retryCountRef.current = 0

    } catch (err) {
      if (isUnmountedRef.current) return

      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido'
      console.error('❌ Erro na requisição:', errorMessage)

      setError(errorMessage)
      setConnectionStatus('error')

      // Implementar retry logic
      retryCountRef.current++
      if (retryCountRef.current <= MAX_RETRIES) {
        console.log(`🔄 Tentativa ${retryCountRef.current}/${MAX_RETRIES} em ${RETRY_DELAY}ms`)
        setTimeout(() => {
          if (!isUnmountedRef.current) {
            fetchData()
          }
        }, RETRY_DELAY)
      } else {
        console.log('❌ Máximo de tentativas excedido')
        setLoading(false)
      }
    }
  }, [maxMatches])

  // Função para iniciar polling
  const connect = useCallback(() => {
    if (isUnmountedRef.current || !enabled) return

    console.log('🔌 Iniciando polling HTTP...', {
      interval: pollingInterval,
      maxMatches
    })

    // Limpar intervalo anterior se existir
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    // Reset retry counter
    retryCountRef.current = 0

    // Fazer primeira requisição imediatamente
    fetchData()

    // Configurar polling
    intervalRef.current = setInterval(() => {
      if (!isUnmountedRef.current && enabled) {
        fetchData()
      }
    }, pollingInterval)

  }, [fetchData, enabled, pollingInterval, maxMatches])

  // Função para parar polling
  const disconnect = useCallback(() => {
    console.log('🛑 Parando polling HTTP')

    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }

    setConnectionStatus('disconnected')
  }, [])

  // Função para refetch manual
  const refetch = useCallback(async () => {
    console.log('🔄 Refetch manual solicitado')
    await fetchData()
  }, [fetchData])

  // Effect para iniciar/parar polling baseado em enabled
  useEffect(() => {
    if (enabled) {
      connect()
    } else {
      disconnect()
    }

    return () => {
      disconnect()
    }
  }, [enabled, connect, disconnect])

  // Cleanup no unmount
  useEffect(() => {
    return () => {
      console.log('🧹 Limpando hook useLiveResults')
      isUnmountedRef.current = true
      disconnect()
    }
  }, [disconnect])

  return {
    matches,
    totalMatches,
    lastUpdated,
    loading,
    error,
    connectionStatus,
    refetch,
    disconnect,
    connect
  }
}
